import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import mongoose from 'mongoose';

@Schema({ _id: false })
export class MeasurementRecord {
  @Prop({ type: Number }) 
  value: number;

  @Prop({ type: Date })
  recordedAt: Date;
}

@Schema({ _id: false })
export class MeasurementGoal {
  @Prop({ type: Number }) 
  target: number;

  @Prop({ type: String })
  bodyPart: string;
}

@Schema({ timestamps: true })
export class BodyMeasurement {

  @Prop({ type: mongoose.Schema.Types.ObjectId, ref: 'Users' })
  userId: string;

  @Prop({ type: MeasurementRecord, required:true })
  neck: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  chest: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  shoulder: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  leftArm: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  rightArm: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  waist: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  abdomen: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  hip: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  leftThigh: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  rightThigh: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  leftCalf: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  rightCalf: MeasurementRecord;

  @Prop({ type: MeasurementRecord, required:true })
  goals: MeasurementGoal;

  @Prop({ type: MeasurementRecord, required:true })
  customMeasurements: Map<string, MeasurementRecord>;
}

export const BodyMeasurementSchema = SchemaFactory.createForClass(BodyMeasurement);
