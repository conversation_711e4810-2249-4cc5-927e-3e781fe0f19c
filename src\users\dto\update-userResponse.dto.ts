import { ApiProperty } from '@nestjs/swagger';
import { IHealthAssessment, IDiabetesInfo, IBloodPressureInfo, IMentalHealthInfo, ISleepInfo, IFitnessInfo, IMeasurementReminders } from '../interfaces/users.interface';

export class UpdateUserResponseDto {
    @ApiProperty({
        description: 'Unique identifier for the user',
        example: '507f1f77bcf86cd799439011',
    })
    _id: string;

    @ApiProperty({
        description: 'User\'s first name',
        example: '<PERSON>',
    })
    firstName: string;

    @ApiProperty({
        description: 'User\'s last name',
        example: 'Doe',
    })
    lastName: string;

    @ApiProperty({
        description: 'User\'s email address',
        example: '<EMAIL>',
    })
    email: string;

    @ApiProperty({
        description: 'User\'s mobile phone number',
        example: '+**********',
    })
    mobileNumber: string;

    @ApiProperty({
        description: 'User\'s gender',
        example: 'Male',
        enum: ['Male', 'Female', 'Other'],
    })
    gender: string;

    @ApiProperty({
        description: 'User\'s date of birth',
        example: '1990-01-15',
    })
    dob: string;

    @ApiProperty({
        description: 'Whether the user\'s account is verified',
        example: false,
        default: false,
    })
    isVerified: boolean;

    @ApiProperty({
        description: 'User\'s health assessment information including physical measurements and health conditions',
        example: {
            ageRange: '25-34',
            height: 175,
            weight: 70,
            bmi: 22.9,
            bodyFatPercentage: 15.5,
            conditionsToManage: ['Diabetes', 'Hypertension']
        },
        required: false
    })
    healthAssessment?: IHealthAssessment;

    @ApiProperty({
        description: 'Diabetes-specific information including type, medication, and glucose levels',
        example: {
            diabetesType: 'Type 2',
            diabetesDiagnosedSince: '2020',
            takesMedication: true,
            medications: ['Metformin', 'Insulin'],
            recentHbA1c: 7.2,
            rememberHbA1c: true,
            lastTestDuration: '3 months',
            recentFastingGlucose: 120,
            diabetesMotivationLevel: 8
        },
        required: false
    })
    diabetesInfo?: IDiabetesInfo;

    @ApiProperty({
        description: 'Blood pressure and cardiovascular health information',
        example: {
            hyperTension: true,
            diagonisedYear: 2019,
            bpSystolic: 140,
            bpDiastolic: 90,
            heartRate: 75,
            rememberBp: true
        },
        required: false
    })
    bloodPressureInfo?: IBloodPressureInfo;

    @ApiProperty({
        description: 'Mental health assessment including anxiety levels and emotional patterns',
        example: {
            comorbidities: ['Anxiety', 'Depression'],
            anxietyLevel: 'Moderate',
            commonEmotions: ['Stressed', 'Worried', 'Hopeful']
        },
        required: false
    })
    mentalHealthInfo?: IMentalHealthInfo;

    @ApiProperty({
        description: 'Sleep quality and patterns information',
        example: {
            sleepQualityRating: 'Fair',
            troubleFallingAsleep: 'Sometimes',
            sleepCondition: 'Insomnia'
        },
        required: false
    })
    sleepInfo?: ISleepInfo;

    @ApiProperty({
        description: 'Fitness and exercise-related information',
        example: {
            fitnessLevel: 'Moderate',
            motivationInsightsAccepted: true
        },
        required: false
    })
    fitnessInfo?: IFitnessInfo;

    @ApiProperty({
        description: 'Measurement reminder preferences for health tracking',
        example: {
            bpMeasurementReminders: true,
            bloodSugarMeasurementReminders: true
        },
        required: false
    })
    Reminders?: IMeasurementReminders;

    @ApiProperty({
        description: 'Timestamp when the user was created',
        example: '2024-01-15T10:30:00.000Z',
    })
    createdAt: Date;

    @ApiProperty({
        description: 'Timestamp when the user was last updated',
        example: '2024-01-15T10:30:00.000Z',
    })
    updatedAt: Date;
}
