import { ApiProperty } from "@nestjs/swagger";
import { IMeasurementGoal, IMeasurementRecord } from "../interfaces/bodyMeasure.interfaces";

export class CreateBodyMeasureDto {
    @ApiProperty({
        description: 'Measurement record for the neck',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    neck: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the chest',
        example: {
            value: 40,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    chest: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the shoulder',
        example: {
            value: 42,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    shoulder: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    leftArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right arm',
        example: {
            value: 32,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    rightArm: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the waist',
        example: {
            value: 30,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    waist: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the abdomen',
        example: {
            value: 34,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    abdomen: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the hip',
        example: {
            value: 38,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    hip: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    leftThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right thigh',
        example: {
            value: 48,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    rightThigh: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the left calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    leftCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement record for the right calf',
        example: {
            value: 36,
            recordedAt: '2024-01-15T10:30:00.000Z',
        },
    })
    rightCalf: IMeasurementRecord;

    @ApiProperty({
        description: 'Measurement goal for a specific body part',
        example: {
            target: 32,
            bodyPart: 'waist',
        },
    })
    goals: IMeasurementGoal;
    @ApiProperty({
        description: 'Custom measurements for additional body parts',
        example: new Map([['bicep', { value: 30, recordedAt: '2024-01-15T10:30:00.000Z' }]]),
    })
    customMeasurements: Map<string, IMeasurementRecord>;

}
