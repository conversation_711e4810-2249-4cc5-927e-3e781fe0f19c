import { HttpException, Injectable, InternalServerErrorException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { BodyMeasurement } from './schema/bodyMeasure.schema';
import { Model } from 'mongoose';
import { IBodyMeasurement } from './interfaces/bodyMeasure.interfaces';
import { CreateBodyMeasureDto } from './dto/createBodyMeasure.dto';

@Injectable()
export class BodyMeasurementService {
  constructor(
    @InjectModel(BodyMeasurement.name)
    private bodyMeasureModel: Model<IBodyMeasurement>,
  ) {}

  async create(bodyMeasureDto: CreateBodyMeasureDto): Promise<IBodyMeasurement> {
    try {
      return await this.bodyMeasureModel.create(bodyMeasureDto);
    } catch (error) {
      throw new InternalServerErrorException(`Failed to create body measurement: ${error.message}`);
    }
  }

  async findAll(): Promise<IBodyMeasurement[]> {
    try {
      return await this.bodyMeasureModel.find().exec();
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch body measurements: ${error.message}`);
    }
  }

  async findOne(id: string): Promise<IBodyMeasurement> {
    try {
      const bodyMeasure = await this.bodyMeasureModel.findById(id).exec();
      if (!bodyMeasure) {
        throw new HttpException('Body measurement not found', 404);
      }
      return bodyMeasure;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to fetch body measurement: ${error.message}`);
    }
  }

  async update(id: string, bodyMeasureDto: CreateBodyMeasureDto): Promise<IBodyMeasurement> {
    try {
      const bodyMeasure = await this.bodyMeasureModel.findByIdAndUpdate(id, bodyMeasureDto, { new: true }).exec();
      if (!bodyMeasure) {
        throw new HttpException('Body measurement not found', 404);
      }
      return bodyMeasure;
    } catch (error) {
      throw new InternalServerErrorException(`Failed to update body measurement: ${error.message}`);
    }
  }

  async delete(id: string): Promise<string> {
    try {
      const bodyMeasure = await this.bodyMeasureModel.findByIdAndDelete(id).exec();
      if (!bodyMeasure) {
        throw new HttpException('Body measurement not found', 404);
      }
      return 'Body measurement deleted';
    } catch (error) {
      throw new InternalServerErrorException(`Failed to delete body measurement: ${error.message}`);
    }
  }
}
