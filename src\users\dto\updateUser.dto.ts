import { PartialType } from "@nestjs/swagger";
import { CreateUserDto } from "./createUser.dto";
import { ApiProperty } from '@nestjs/swagger';
import { Prop } from "@nestjs/mongoose";
import { IMeasurementReminders, IHealthAssessment, IDiabetesInfo, IBloodPressureInfo, IMentalHealthInfo, ISleepInfo, IFitnessInfo } from "../interfaces/users.interface";

export class UpdateUserDto extends PartialType(CreateUserDto){

    @ApiProperty({
        description: 'User\'s health assessment information including physical measurements and health conditions',
        example: {
            ageRange: '25-34',
            height: 175,
            weight: 70,
            bmi: 22.9,
            bodyFatPercentage: 15.5,
            conditionsToManage: ['Diabetes', 'Hypertension']
        },
        required: false
    })
    @Prop({ type: Object })
    healthAssessment?: IHealthAssessment;

    @ApiProperty({
        description: 'Diabetes-specific information including type, medication, and glucose levels',
        example: {
            diabetesType: 'Type 2',
            diabetesDiagnosedSince: '2020',
            takesMedication: true,
            medications: ['Metformin', 'Insulin'],
            recentHbA1c: 7.2,
            rememberHbA1c: true,
            lastTestDuration: '3 months',
            recentFastingGlucose: 120,
            diabetesMotivationLevel: 8
        },
        required: false
    })
    @Prop({ type: Object })
    diabetesInfo?: IDiabetesInfo;

    @ApiProperty({
        description: 'Blood pressure and cardiovascular health information',
        example: {
            hyperTension: true,
            diagonisedYear: 2019,
            bpSystolic: 140,
            bpDiastolic: 90,
            heartRate: 75,
            rememberBp: true
        },
        required: false
    })
    @Prop({ type: Object })
    bloodPressureInfo?: IBloodPressureInfo;

    @ApiProperty({
        description: 'Mental health assessment including anxiety levels and emotional patterns',
        example: {
            comorbidities: ['Anxiety', 'Depression'],
            anxietyLevel: 'Moderate',
            commonEmotions: ['Stressed', 'Worried', 'Hopeful']
        },
        required: false
    })
    @Prop({ type: Object })
    mentalHealthInfo?: IMentalHealthInfo;

    @ApiProperty({
        description: 'Sleep quality and patterns information',
        example: {
            sleepQualityRating: 'Fair',
            troubleFallingAsleep: 'Sometimes',
            sleepCondition: 'Insomnia'
        },
        required: false
    })
    @Prop({ type: Object })
    sleepInfo?: ISleepInfo;

    @ApiProperty({
        description: 'Measurement reminder preferences for health tracking',
        example: {
            bpMeasurementReminders: true,
            bloodSugarMeasurementReminders: true
        },
        required: false
    })

    @ApiProperty({
        description: 'Fitness and exercise-related information',
        example: {
            fitnessLevel: 'Moderate',
            motivationInsightsAccepted: true
        },
        required: false
    })

    @Prop({ type: Object })
    fitnessInfo?: IFitnessInfo;


    @ApiProperty({
        description: 'Measurement reminder preferences for health tracking',
        example: {
            bpMeasurementReminders: true,
            bloodSugarMeasurementReminders: true
        },
        required: false
    })
    @Prop({ type: Object })
    Reminders?: IMeasurementReminders;

};