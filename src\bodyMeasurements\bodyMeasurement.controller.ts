import { Body, Controller, Delete, Get, Param, Patch, Post, UseGuards } from "@nestjs/common";
import { ApiBearerAuth, ApiBody, ApiOperation, ApiResponse, ApiTags } from "@nestjs/swagger";
import { BodyMeasurementService } from "./bodyMeasurement.service";
import { CreateBodyMeasureDto } from "./dto/createBodyMeasure.dto";
import { AuthGuard } from "src/auth/auth.guard";

@ApiTags('BodyMeasurements')
@UseGuards(AuthGuard)
@Controller('body-measurements')
export class BodyMeasurementController {
  constructor(private readonly bodyMeasurementService: BodyMeasurementService) {}

  @Post()
  @ApiBearerAuth('Bearer-auth')
  @ApiOperation({
    summary: 'Create body measurement',
    description: 'Create a new body measurement record',
  })
  @ApiBody({
    type: CreateBodyMeasureDto,
    description: 'Body measurement data',
  })
  @ApiResponse({
    status: 201,
    description: 'Body measurement created',
    type: BodyMeasureResponseDto,
  })
  async createBodyMeasure(@Body() bodyMeasureDto: CreateBodyMeasureDto) {
    return this.bodyMeasurementService.create(bodyMeasureDto);
  }

  @Get()
  @ApiBearerAuth('Bearer-auth')
  @ApiOperation({
    summary: 'Get all body measurements',
    description: 'Retrieve all body measurement records',
  })
  @ApiResponse({
    status: 200,
    description: 'Body measurements found',
    type: [BodyMeasureResponseDto],
  })
  async getAllBodyMeasurements() {
    return this.bodyMeasurementService.findAll();
  }

  @Get(':id')
  @ApiBearerAuth('Bearer-auth')
  @ApiOperation({
    summary: 'Get body measurement by ID',
    description: 'Retrieve body measurement by its unique identifier',
  })
  @ApiResponse({
    status: 200,
    description: 'Body measurement found',
    type: BodyMeasureResponseDto,
  })
  async getBodyMeasureById(@Param('id') id: string) {
    return this.bodyMeasurementService.findOne(id);
  }

  @Patch(':id')
  @ApiBearerAuth('Bearer-auth')
  @ApiOperation({
    summary: 'Update body measurement by ID',
    description: 'Update body measurement by its unique identifier',
  })
  @ApiBody({
    type: CreateBodyMeasureDto,
    description: 'Body measurement data',
  })
  @ApiResponse({
    status: 200,
    description: 'Body measurement updated',
    type: BodyMeasureResponseDto,
  })
  async updateBodyMeasure(
    @Param('id') id: string,
    @Body() bodyMeasureDto: CreateBodyMeasureDto,
  ) {
    return this.bodyMeasurementService.update(id, bodyMeasureDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: 'Delete body measurement by ID',
    description: 'Delete body measurement by its unique identifier',
  })
  @ApiResponse({
    status: 200,
    description: 'Body measurement deleted',
    type: BodyMeasureResponseDto,
  })
  async deleteBodyMeasure(@Param('id') id: string) {
    return this.bodyMeasurementService.delete(id);
  }
}
